
clear all; close all; clc;
%只考虑了自由空间传播损耗

f_B1C = 1575.42 ; %MHz
f_B2a = 1176.45 ; %MHZ
f_DME = 1172;       % DME干扰信号频率 (MHz)
f_ADSB = 1090;    %MHz 


EIRP_DMEN  = 37;      % 等效全向辐射功率 (dBW) 
EIRP_DMEP  = 25;
EIRP_ADSB = 27; 

%脉冲重复频率
T_DME = 1/1100 ;
T_ADSB = 1/500 ;

Ai_DME = cal_Ai(f_DME);
Ai_ADSB = cal_Ai(f_ADSB);

Pd_DME = -89 + Ai_DME;           
Pd_ADSB = -92 + Ai_ADSB;


d_DMEN = 300;        %总共算多少海里
r_DMEN_min = 600/1852;
r_DMEN_max = 200;         %标红的点

d_DMEP = 60;
r_DMEP_min = 150/1852;
r_DMEP_max = 22;        

d_ADSB = 150;
r_ADSB_min = 600/1852;
r_ADSB_max = 90;  

%接收机带宽
B_B1C = 4e6; 
B_B2a = 10.23e6 ;  %B1C-4MHz,B2a-10.23MHz
B_DME = 1e6;
B_ADSB = 2.6e6;  %干扰信号带宽

%到达天线处干扰信号的功率

[Pa_DMEN,r_nm_N] = cal_Pa(EIRP_DMEN,f_DME,d_DMEN);
[Pa_DMEP,r_nm_P] = cal_Pa(EIRP_DMEP,f_DME,d_DMEP);
[Pa_ADSB,r_nm_ES] = cal_Pa(EIRP_ADSB,f_ADSB,d_ADSB);

a = interp1(r_nm_N, Pa_DMEN, r_DMEN_min);
b = interp1(r_nm_N, Pa_DMEN, r_DMEN_max);

c = interp1(r_nm_P, Pa_DMEP, r_DMEP_min);
d = interp1(r_nm_P, Pa_DMEP, r_DMEP_max);

e = interp1(r_nm_ES, Pa_ADSB, r_ADSB_min);
f = interp1(r_nm_ES, Pa_ADSB, r_ADSB_max);



%噪声功率密度（常温）
N_0 = -204.3; %dBW/Hz

%噪声功率
P_n_B1C = -204.3+10*log10(B_B1C) ;
P_n_B2a = -204.3+10*log10(B_B2a) ;

%衰减
%DME频谱衰减
%滤波器衰减

Loss_DME_B1C = -(65 + 50); %DME频谱衰减+B1C滤波选择特性
Loss_DME_B2a = 0;       %1172MHz落在B2a带内无衰减

Loss_ADSB_B1C = -(60 + 50);
Loss_ADSB_B2a = -40;

%频谱重叠系数
L_DME_B1C = 10*log10(B_DME/B_B1C) ;
L_DME_B2a = 10*log10(B_DME/B_B2a) ;
L_ADSB_B1C = 10*log10(B_ADSB/B_B1C) ;
L_ADSB_B2a = 10*log10(B_ADSB/B_B2a) ;

%占空比衰减
%占空比
Duty_DME = (2*3.5e-6)/T_DME;
Duty_ADSB = (112*0.5e-6)/T_ADSB;

L_Duty_DME = 10*log10(Duty_DME) ;
L_Duty_ADSB = 10*log10(Duty_ADSB) ;

%干噪比
JNR_B1C_DMEN = Pa_DMEN + Loss_DME_B1C  + L_Duty_DME - P_n_B1C ;
JNR_B2a_DMEN = Pa_DMEN + Loss_DME_B2a + L_DME_B2a + L_Duty_DME - P_n_B2a;

JNR_B1C_DMEP = Pa_DMEP + Loss_DME_B1C  + L_Duty_DME - P_n_B1C;
JNR_B2a_DMEP = Pa_DMEP + Loss_DME_B2a + L_DME_B2a + L_Duty_DME - P_n_B2a;

JNR_B1C_ADSB = Pa_ADSB + Loss_ADSB_B1C + L_Duty_ADSB - P_n_B1C;
JNR_B2a_ADSB = Pa_ADSB + Loss_ADSB_B2a + L_ADSB_B2a + L_Duty_ADSB - P_n_B2a;


% DME干扰分析图
figure('Name','航路DME干扰分析');
set(gcf, 'Position', [100, 100, 1000, 500]);
% 航路DME对B1C（左上）
subplot(1,2,1);
plot(r_nm_N, JNR_B1C_DMEN, 'b', 'LineWidth', 1.5);
hold on;
plot(r_DMEN_min, interp1(r_nm_N, JNR_B1C_DMEN, r_DMEN_min), 'ro', 'MarkerSize', 6, 'MarkerFaceColor', 'r'); 
line([0,r_nm_N(end)],[49,49],'Color','red','linestyle','--');
plot(r_DMEN_max, interp1(r_nm_N, JNR_B1C_DMEN, r_DMEN_max), 'ro', 'MarkerSize', 6, 'MarkerFaceColor', 'r');

% line([0,r_nm_N(end)],[Pd_DME,Pd_DME],'linestyle','--');
xlabel('距离 (海里)');
ylabel('干噪比 (dB)');
title('航路DME对B1C峰值干噪比随距离变化');
ylim([-120 80]);
grid on;
legend('干噪比', '理想干噪比范围','B1C失锁干噪比（49dB）', 'Location', 'best');
set(gca, 'FontSize', 11, 'FontWeight', 'bold');

% 航路DME对B2a（右上）
subplot(1,2,2);
plot(r_nm_N, JNR_B2a_DMEN, 'b', 'LineWidth', 1.5);
hold on;
plot(r_DMEN_min, interp1(r_nm_N, JNR_B2a_DMEN, r_DMEN_min), 'ro', 'MarkerSize', 6, 'MarkerFaceColor', 'r'); 
line([0,r_nm_N(end)],[30,30],'Color','red','linestyle','--');
plot(r_DMEN_max, interp1(r_nm_N, JNR_B2a_DMEN, r_DMEN_max), 'ro', 'MarkerSize', 6, 'MarkerFaceColor', 'r');

annotation('textbox', [0.7, 0.5, 0.2, 0.1], 'String',...
           {sprintf('等效全向辐射功率: %d dBW', EIRP_DMEN),...
            sprintf('工作频率: %d MHz', f_DME)},...
           'FitBoxToText', 'on');
xlabel('距离 (海里)');
ylabel('干噪比 (dB)');
 title('航路DME对B2a峰值干噪比随距离变化');
ylim([-120 80]);
grid on;

legend('干噪比', '理想干噪比范围','B2a失锁干噪比（30dB）', 'Location', 'best');
set(gca, 'FontSize', 11, 'FontWeight', 'bold');

figure('Name','终端DME干扰分析');
set(gcf, 'Position', [100, 100, 1000, 500]);

% 终端DME对B1C（左下）
subplot(1,2,1);
plot(r_nm_P, JNR_B1C_DMEP, 'b', 'LineWidth', 1.5);
hold on;
plot(r_DMEP_min, interp1(r_nm_P, JNR_B1C_DMEP, r_DMEP_min), 'ro', 'MarkerSize', 6, 'MarkerFaceColor', 'r'); 
line([0,r_nm_P(end)],[49,49],'Color','red','linestyle','--');
plot(r_DMEP_max, interp1(r_nm_P, JNR_B1C_DMEP, r_DMEP_max), 'ro', 'MarkerSize', 6, 'MarkerFaceColor', 'r');

legend('干噪比', '理想干噪比范围','B1C失锁干噪比（49dB）', 'Location', 'best');
xlabel('距离 (海里)');
ylabel('干噪比 (dB)');
 title('终端DME对B1C峰值干噪比随距离变化');
ylim([-120 80]);
grid on;

set(gca, 'FontSize', 11, 'FontWeight', 'bold');

% 终端DME对B2a（右下）
subplot(1,2,2);
plot(r_nm_P, JNR_B2a_DMEP, 'b', 'LineWidth', 1.5);
hold on;
plot(r_DMEP_min, interp1(r_nm_P, JNR_B2a_DMEP, r_DMEP_min), 'ro', 'MarkerSize', 6, 'MarkerFaceColor', 'r'); 
line([0,r_nm_P(end)],[30,30],'Color','red','linestyle','--');
plot(r_DMEP_max, interp1(r_nm_P, JNR_B2a_DMEP, r_DMEP_max), 'ro', 'MarkerSize', 6, 'MarkerFaceColor', 'r');

annotation('textbox', [0.7, 0.5, 0.2, 0.1], 'String',...
           {sprintf('等效全向辐射功率: %d dBW', EIRP_DMEP),...
            sprintf('工作频率: %d MHz', f_DME)},...
           'FitBoxToText', 'on');
legend('干噪比', '理想干噪比范围','B2a失锁干噪比（30dB）', 'Location', 'best');
xlabel('距离 (海里)');
ylabel('干噪比 (dB)');
title('终端DME对B2a峰值干噪比随距离变化');
ylim([-120 80]);
grid on;

set(gca, 'FontSize', 11, 'FontWeight', 'bold');

% ADS-B干扰分析图（水平布局）
figure('Name','ADS-B干扰分析');
set(gcf, 'Position', [100, 100, 1000, 500]);

% ADS-B对B1C（左）
subplot(1,2,1);
plot(r_nm_ES, JNR_B1C_ADSB, 'b', 'LineWidth', 1.5);
hold on;
plot(r_ADSB_min, interp1(r_nm_ES, JNR_B1C_ADSB, r_ADSB_min), 'ro', 'MarkerSize', 6, 'MarkerFaceColor', 'r');
line([0,r_nm_ES(end)],[54,54],'Color','red','linestyle','--');
plot(r_ADSB_max, interp1(r_nm_ES, JNR_B1C_ADSB, r_ADSB_max), 'ro', 'MarkerSize', 6, 'MarkerFaceColor', 'r');

xlabel('距离 (海里)');
ylabel('干噪比 (dB)');
 title('1090 ES对B1C峰值干噪比随距离变化');
ylim([-120 80]);
grid on;

legend('干噪比', '理想干噪比范围','B1C失锁干噪比（54dB）', 'Location', 'best');
set(gca, 'FontSize', 11, 'FontWeight', 'bold');

% ADS-B对B2a（右）
subplot(1,2,2);
plot(r_nm_ES, JNR_B2a_ADSB, 'b', 'LineWidth', 1.5);
hold on;
plot(r_ADSB_min, interp1(r_nm_ES, JNR_B2a_ADSB, r_ADSB_min), 'ro', 'MarkerSize', 6, 'MarkerFaceColor', 'r'); 
line([0,r_nm_ES(end)],[44,44],'Color','red','linestyle','--');
plot(r_ADSB_max, interp1(r_nm_ES, JNR_B2a_ADSB, r_ADSB_max), 'ro', 'MarkerSize', 6, 'MarkerFaceColor', 'r');

annotation('textbox', [0.7, 0.5, 0.2, 0.1], 'String',...
           {sprintf('等效全向辐射功率: %d dBW', EIRP_ADSB),...
            sprintf('工作频率: %d MHz',f_ADSB)},...
           'FitBoxToText', 'on');
legend('干噪比', '理想干噪比范围','B2a失锁干噪比（44dB）', 'Location', 'best');
xlabel('距离 (海里)');
ylabel('干噪比 (dB)');
 title('1090 ES对B2a峰值干噪比随距离变化');
ylim([-120 80]);
grid on;

set(gca, 'FontSize', 11, 'FontWeight', 'bold');


function [Ai] = cal_Ai(f_sig)
lambda = 299.7925 / f_sig;            % 波长 (米)
Ai = 10*log10(lambda^2 / (4*pi));     % 各向同性天线接收功率密度系数 (dB/m^2)
end

function [y,r_nm_vec] = cal_Pa(EIRP,f,d)

EIRP_dBW = EIRP;      % 等效全向辐射功率 (dBW) 
f_sig = f;       % 频率 (MHz)

nm = 1852;          % 1海里=1852米
r_nm_vec = linspace(0.01,d, 1000);  % 距离
r_km_vec = r_nm_vec * nm / 1000;      % 转换为千米



% 计算损耗 (dB)
L_br = 32.45 + 20*log10(f_sig) + 20*log10(r_km_vec);

% 计算天线处功率密度 (dBW/m^2)

%SR_q = EIRP_dBW - L_br - Ai ;
pa = EIRP_dBW - L_br ;

y = pa;


end