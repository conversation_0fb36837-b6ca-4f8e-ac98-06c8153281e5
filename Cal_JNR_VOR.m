clear all; close all; clc;
%只考虑了自由空间传播损耗

f_B1C = 1575.42 ; %MHz
f_B2a = 1176.45 ; %MHZ

% 根据黄雅婷.txt中的实际测量数据设置VOR参数
% VOR第14次谐波频点直接干扰B1C，第10次谐波频点直接干扰B2a
f_VOR = 112.45;       % VOR基本频率 (MHz)
f_VOR_14 = 1574.3;    % 第14次谐波频点(MHz)，直接使用测量值
f_VOR_10 = 1174.5;    % 第10次谐波频点(MHz)，直接使用测量值

% VOR发射功率参数，根据测试数据
EIRP_VOR_dBm = 15;    % 发射功率 (dBm)
EIRP_VOR = EIRP_VOR_dBm - 30;  % 转换为dBW

% VOR谐波功率衰减（谐波功率相比基频的衰减）
% 这里根据实际测量结果反推
Harmonic_14_Loss = 30;  % 第14次谐波衰减 (dB)
Harmonic_10_Loss = 25;  % 第10次谐波衰减 (dB)

% VOR有效EIRP (考虑谐波衰减后)
EIRP_VOR_14 = EIRP_VOR - Harmonic_14_Loss;
EIRP_VOR_10 = EIRP_VOR - Harmonic_10_Loss;

% 计算不同距离下的功率
Ai_VOR_14 = cal_Ai(f_VOR_14);
Ai_VOR_10 = cal_Ai(f_VOR_10);

% 参考干噪比数据，根据黄雅婷.txt中测试结果
JNR_B1C_Measured = 24.9;  % 测量得到的B1C干噪比 (dB)

% 计算距离范围
d_VOR = 150;        % 总共算多少海里
r_VOR_min = 1;      % 最小距离 (海里)
r_VOR_max = 100;    % 最大距离 (海里)

%接收机带宽
B_B1C = 4e6;        % B1C带宽4MHz (根据测试数据)
B_B2a = 10.23e6;    % B2a带宽10.23MHz (根据测试数据)
B_VOR = 0.025e6;    % VOR带宽约为25kHz (根据教程数据)

% 到达天线处干扰信号的功率
[Pa_VOR_14, r_nm_14] = cal_Pa(EIRP_VOR_14, f_VOR_14, d_VOR);
[Pa_VOR_10, r_nm_10] = cal_Pa(EIRP_VOR_10, f_VOR_10, d_VOR);

% 计算关键点的功率值
a = interp1(r_nm_14, Pa_VOR_14, r_VOR_min);
b = interp1(r_nm_14, Pa_VOR_14, r_VOR_max);

c = interp1(r_nm_10, Pa_VOR_10, r_VOR_min);
d = interp1(r_nm_10, Pa_VOR_10, r_VOR_max);

% 噪声功率密度（常温）
N_0 = -204.3; %dBW/Hz

% 噪声功率
P_n_B1C = -204.3 + 10*log10(B_B1C);
P_n_B2a = -204.3 + 10*log10(B_B2a);

% 衰减
% 频谱重叠的影响
% 频谱落在B1C信号频带内，频谱偏移B1C中心频率1.12MHz
% 频谱落在B2a信号频带内，频谱偏移B2a中心频率0.95MHz
Loss_VOR_14_B1C = -3;     % 第14次谐波对B1C的频谱衰减
Loss_VOR_10_B2a = -2;     % 第10次谐波对B2a的频谱衰减

% 频谱重叠系数
L_VOR_B1C = 10*log10(B_VOR/B_B1C);
L_VOR_B2a = 10*log10(B_VOR/B_B2a);

% VOR是调幅信号，调制损失
L_Modulation = 10*log10(0.85^2);  % 调制损失

% 干噪比计算
JNR_B1C_VOR = Pa_VOR_14 + Loss_VOR_14_B1C + L_VOR_B1C + L_Modulation - P_n_B1C;
JNR_B2a_VOR = Pa_VOR_10 + Loss_VOR_10_B2a + L_VOR_B2a + L_Modulation - P_n_B2a;

% 计算与实测数据的校准系数 (在距离为1海里处校准)
Calibration_Factor = JNR_B1C_Measured - interp1(r_nm_14, JNR_B1C_VOR, 1);

% 校准后的干噪比
JNR_B1C_VOR_Calibrated = JNR_B1C_VOR + Calibration_Factor;
JNR_B2a_VOR_Calibrated = JNR_B2a_VOR + Calibration_Factor;

% VOR干扰分析图
figure('Name', 'VOR干扰对北斗卫星导航系统的影响分析');
set(gcf, 'Position', [100, 100, 1000, 500]);

% VOR第14次谐波对B1C的影响
subplot(1, 2, 1);
plot(r_nm_14, JNR_B1C_VOR_Calibrated, 'b', 'LineWidth', 1.5);
hold on;
plot(r_VOR_min, interp1(r_nm_14, JNR_B1C_VOR_Calibrated, r_VOR_min), 'ro', 'MarkerSize', 6, 'MarkerFaceColor', 'r');
line([0, r_nm_14(end)], [49, 49], 'Color', 'red', 'linestyle', '--');
plot(r_VOR_max, interp1(r_nm_14, JNR_B1C_VOR_Calibrated, r_VOR_max), 'ro', 'MarkerSize', 6, 'MarkerFaceColor', 'r');
plot(1, JNR_B1C_Measured, 'go', 'MarkerSize', 8, 'MarkerFaceColor', 'g');  % 实测点

xlabel('距离 (海里)');
ylabel('干噪比 (dB)');
title('VOR第14次谐波对B1C峰值干噪比随距离变化');
ylim([-120 80]);
grid on;
legend('干噪比', '计算点', 'B1C失锁干噪比（49dB）', '计算点', '实测点', 'Location', 'best');
set(gca, 'FontSize', 11, 'FontWeight', 'bold');

% VOR第10次谐波对B2a的影响
subplot(1, 2, 2);
plot(r_nm_10, JNR_B2a_VOR_Calibrated, 'b', 'LineWidth', 1.5);
hold on;
plot(r_VOR_min, interp1(r_nm_10, JNR_B2a_VOR_Calibrated, r_VOR_min), 'ro', 'MarkerSize', 6, 'MarkerFaceColor', 'r');
line([0, r_nm_10(end)], [30, 30], 'Color', 'red', 'linestyle', '--');
plot(r_VOR_max, interp1(r_nm_10, JNR_B2a_VOR_Calibrated, r_VOR_max), 'ro', 'MarkerSize', 6, 'MarkerFaceColor', 'r');

annotation('textbox', [0.7, 0.5, 0.3, 0.15], 'String', ...
           {sprintf('VOR发射功率: %.1f dBm', EIRP_VOR_dBm), ...
            sprintf('第14次谐波频率: %.1f MHz', f_VOR_14), ...
            sprintf('第10次谐波频率: %.1f MHz', f_VOR_10), ...
            sprintf('实测B1C干噪比: %.1f dB', JNR_B1C_Measured)}, ...
           'FitBoxToText', 'on');
xlabel('距离 (海里)');
ylabel('干噪比 (dB)');
title('VOR第10次谐波对B2a峰值干噪比随距离变化');
ylim([-120 80]);
grid on;
legend('干噪比', '计算点', 'B2a失锁干噪比（30dB）', '计算点', 'Location', 'best');
set(gca, 'FontSize', 11, 'FontWeight', 'bold');

% 函数定义
function [Ai] = cal_Ai(f_sig)
lambda = 299.7925 / f_sig;            % 波长 (米)
Ai = 10*log10(lambda^2 / (4*pi));     % 各向同性天线接收功率密度系数 (dB/m^2)
end

function [y, r_nm_vec] = cal_Pa(EIRP, f, d)
EIRP_dBW = EIRP;      % 等效全向辐射功率 (dBW)
f_sig = f;            % 频率 (MHz)

nm = 1852;            % 1海里=1852米
r_nm_vec = linspace(0.01, d, 1000);  % 距离
r_km_vec = r_nm_vec * nm / 1000;     % 转换为千米

% 计算损耗 (dB)
L_br = 32.45 + 20*log10(f_sig) + 20*log10(r_km_vec);

% 计算A_i (各向同性天线有效面积)
Ai = cal_Ai(f_sig);

% 计算天线处功率密度 (dBW/m^2) - 根据公式 S_q = EIRP - L_br - A_i
pa = EIRP_dBW - L_br - Ai;

y = pa;
end